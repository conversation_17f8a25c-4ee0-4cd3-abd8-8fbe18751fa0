package com.autoai.aiosservice

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.autoai.aiosservice.databinding.ActivityTestBinding
import com.autoai.car.common.utils.LogUtils

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 测试页面
 */
class TestActivity: AppCompatActivity() {

    private lateinit var binding: ActivityTestBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initView()
    }

    private fun initView() {
        binding.btnSendPrompt.setOnClickListener {
            // 发送Prompt
            LogUtils.d(TAG, "initView: 发送Prompt")

        }
    }

    companion object {
        private const val TAG = "TestActivity"
    }
}